import pygame
import numpy as np
import math
import sys

# --- Constants ---
WIDTH, HEIGHT = 800, 800
FPS = 60
HEX_RADIUS = 300
BALL_RADIUS = 18
GRAVITY = 0.5
FRICTION = 0.99
ELASTICITY = 0.85
BALL_COLOR = (80, 180, 255)
HEX_COLOR = (200, 200, 200)
BG_COLOR = (30, 30, 40)
TEXT_COLOR = (255, 255, 255)

pygame.init()
screen = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption("Bouncing Balls in a Spinning Hexagon")
clock = pygame.time.Clock()
font = pygame.font.SysFont("consolas", 20)

center = np.array([WIDTH // 2, HEIGHT // 2])

# --- Helper Functions ---

def hexagon_points(center, radius, angle):
    """Return the 6 points of a regular hexagon, rotated by angle (radians)."""
    points = []
    for i in range(6):
        theta = angle + i * math.pi / 3
        x = center[0] + radius * math.cos(theta)
        y = center[1] + radius * math.sin(theta)
        points.append(np.array([x, y]))
    return points

def draw_text(surface, text, pos, color=TEXT_COLOR, align="topleft"):
    img = font.render(text, True, color)
    rect = img.get_rect()
    setattr(rect, align, pos)
    surface.blit(img, rect)

def point_line_distance(p, a, b):
    """Return the closest point on line segment ab to point p, and the distance."""
    ab = b - a
    t = np.dot(p - a, ab) / np.dot(ab, ab)
    t = np.clip(t, 0, 1)
    closest = a + t * ab
    dist = np.linalg.norm(p - closest)
    return closest, dist

# --- Classes ---

class Ball:
    def __init__(self, pos, vel=None):
        self.pos = np.array(pos, dtype=float)
        self.vel = np.array(vel if vel is not None else [0, 0], dtype=float)
        self.radius = BALL_RADIUS
        self.color = BALL_COLOR

    def update(self):
        self.vel[1] += GRAVITY
        self.vel *= FRICTION
        self.pos += self.vel

    def draw(self, surface):
        pygame.draw.circle(
            surface, self.color, self.pos.astype(int), self.radius
        )

    def collide_with_ball(self, other):
        delta = other.pos - self.pos
        dist = np.linalg.norm(delta)
        if dist == 0:
            return
        min_dist = self.radius + other.radius
        if dist < min_dist:
            # Overlap, push apart
            overlap = min_dist - dist
            direction = delta / dist
            self.pos -= direction * (overlap / 2)
            other.pos += direction * (overlap / 2)
            # Elastic collision
            v1 = self.vel
            v2 = other.vel
            self.vel = (
                v1
                - np.dot(v1 - v2, direction) * direction * ELASTICITY
            )
            other.vel = (
                v2
                - np.dot(v2 - v1, -direction) * -direction * ELASTICITY
            )

    def collide_with_hexagon(self, hex_points, hex_center, hex_angle, hex_omega):
        for i in range(6):
            a = hex_points[i]
            b = hex_points[(i + 1) % 6]
            closest, dist = point_line_distance(self.pos, a, b)
            if dist < self.radius:
                # Wall normal
                wall = b - a
                normal = np.array([-wall[1], wall[0]])
                normal /= np.linalg.norm(normal)
                # Move ball out of wall
                self.pos += normal * (self.radius - dist)
                # Wall velocity due to rotation
                rel = closest - hex_center
                wall_vel = np.array(
                    [-rel[1], rel[0]]
                ) * hex_omega
                # Reflect velocity
                v_rel = self.vel - wall_vel
                v_n = np.dot(v_rel, normal)
                if v_n < 0:
                    v_rel -= (1 + ELASTICITY) * v_n * normal
                    self.vel = v_rel + wall_vel

# --- Simulation State ---

balls = []
hex_angle = 0.0
hex_omega = 0.01  # radians per frame
hex_paused = False

# --- Main Loop ---

running = True
while running:
    dt = clock.tick(FPS) / 1000.0

    # --- Event Handling ---
    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            running = False

        elif event.type == pygame.MOUSEBUTTONDOWN:
            mouse_pos = np.array(pygame.mouse.get_pos())
            if event.button == 1:  # Left click: add ball
                # Place at mouse, random small velocity
                balls.append(
                    Ball(
                        mouse_pos,
                        np.random.uniform(-3, 3, 2),
                    )
                )
            elif event.button == 3:  # Right click: remove nearest ball
                if balls:
                    dists = [np.linalg.norm(b.pos - mouse_pos) for b in balls]
                    idx = np.argmin(dists)
                    if dists[idx] < 2 * BALL_RADIUS:
                        balls.pop(idx)

        elif event.type == pygame.KEYDOWN:
            if event.key == pygame.K_UP:
                hex_omega += 0.005
            elif event.key == pygame.K_DOWN:
                hex_omega -= 0.005
            elif event.key == pygame.K_r:
                hex_omega *= -1
            elif event.key == pygame.K_SPACE:
                hex_paused = not hex_paused

    # --- Update Hexagon ---
    if not hex_paused:
        hex_angle += hex_omega

    hex_points = hexagon_points(center, HEX_RADIUS, hex_angle)

    # --- Update Balls ---
    for ball in balls:
        ball.update()

    # Ball-ball collisions
    for i in range(len(balls)):
        for j in range(i + 1, len(balls)):
            balls[i].collide_with_ball(balls[j])

    # Ball-hexagon collisions
    for ball in balls:
        ball.collide_with_hexagon(
            hex_points, center, hex_angle, hex_omega if not hex_paused else 0
        )

    # --- Drawing ---
    screen.fill(BG_COLOR)
    # Draw hexagon
    pygame.draw.polygon(
        screen, HEX_COLOR, [p.astype(int) for p in hex_points], 4
    )
    # Draw balls
    for ball in balls:
        ball.draw(screen)

    # --- UI ---
    controls = [
        "Controls:",
        "Left Click: Add Ball",
        "Right Click: Remove Ball",
        "Up/Down: Adjust Rotation Speed",
        "R: Reverse Rotation",
        "Space: Pause/Resume Rotation",
    ]
    for i, line in enumerate(controls):
        draw_text(screen, line, (10, 10 + i * 22), align="topleft")

    settings = [
        f"Rotation Speed: {hex_omega:.3f} rad/frame",
        f"Balls: {len(balls)}",
        f"Rotation: {'Paused' if hex_paused else 'Running'}",
    ]
    for i, line in enumerate(settings):
        draw_text(
            screen,
            line,
            (10, HEIGHT - 70 + i * 22),
            align="topleft",
        )

    pygame.display.flip()

pygame.quit()
sys.exit()
